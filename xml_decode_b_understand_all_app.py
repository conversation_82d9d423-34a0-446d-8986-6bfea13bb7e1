import argparse
import pandas as pd
import os
import gzip
import base64
import multiprocessing as mp
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
import gc
import psutil
from svrkit.core.oss import OssAttrInc
from rawins import Rawins
from io import BytesIO
from utils import load_private_key_from_base64, decrypt_with_rsa, decode_with_pycryptodome
from datetime import datetime, timedelta
from py_mini_racer import MiniRacer
import time
# 根据CPU核心数设置并行度，但不超过系统内存限制
CPU_COUNT = min(mp.cpu_count(), 32)  # 限制最大进程数
CHUNK_SIZE = 1000  # 每个批次处理的行数

# db_conf
mysql_conf = {
    "user": "root",
    "passwd": "9g@gmZh7Hf",
    "port": 3306,
    "host": "***********",
    "db": "mmfd_zhaohui",
    "charset": 'utf8'
}

# # clickhouse conf
ch_conf = {
"host":'**************',
"port": 9000,
"user": 'longvideo_luban',
"password": 'GZqtkM9yG4N6ZKbYsa7r',
"database": 'default' ,
"send_receive_timeout": 120
}

rawins = Rawins(ch_conf, mysql_conf)
rawins.register("marcusdai")

pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)
pd.set_option("display.max_colwidth", None)

credential_file_path = "./.credential/credential.rsa"
private_key = load_private_key_from_base64(credential_file_path)

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024  # MB

def get_data_in_batches(sql_base: str, batch_size: int = 50000):
    """分批获取数据，避免内存溢出"""
    print(f"开始分批查询数据，批次大小: {batch_size}")

    # 首先获取总数据量
    count_sql = f"SELECT COUNT(*) as total FROM ({sql_base}) as subquery"
    count_result = rawins.query_dataframe(count_sql, pretty=False, use_alias=False)
    total_rows = count_result.iloc[0]['total']
    print(f"总数据量: {total_rows} 行")

    # 计算需要的批次数
    num_batches = (total_rows + batch_size - 1) // batch_size
    print(f"将分 {num_batches} 个批次处理")

    all_dataframes = []

    for i in range(num_batches):
        offset = i * batch_size
        batch_sql = f"{sql_base} LIMIT {batch_size} OFFSET {offset}"

        print(f"正在处理第 {i+1}/{num_batches} 批次 (offset: {offset})")
        print(f"当前内存使用: {get_memory_usage():.2f} MB")

        batch_df = rawins.query_dataframe(batch_sql, pretty=False, use_alias=False)
        all_dataframes.append(batch_df)

        # 每处理几个批次就强制垃圾回收
        if (i + 1) % 5 == 0:
            gc.collect()

    # 合并所有批次的数据
    print("正在合并所有批次数据...")
    final_df = pd.concat(all_dataframes, ignore_index=True)

    # 清理中间数据
    del all_dataframes
    gc.collect()

    print(f"数据合并完成，最终数据量: {len(final_df)} 行")
    print(f"合并后内存使用: {get_memory_usage():.2f} MB")

    return final_df

def reverse_kotlin_operation(processed_str: str) -> str:
    print('len:', len(processed_str))
    try:
        compressed_bytes = base64.b64decode(processed_str)
    except Exception as e:
        print("Base64解码失败") 
        return None
    try:
        with gzip.GzipFile(fileobj=BytesIO(compressed_bytes)) as f:
            return f.read().decode('utf-8')
    except gzip.BadGzipFile as e:
        print("GZIP解压失败")
        return None


def process_chunk_worker(chunk_data, private_key_path):
    """多进程工作函数：处理数据块"""
    try:
        # 在子进程中重新加载私钥
        private_key = load_private_key_from_base64(private_key_path)

        chunk_df = pd.DataFrame(chunk_data)

        # 1. 拼接所有列（处理可能的空值）
        chunk_df['combined'] = chunk_df.apply(
            lambda row: ''.join([
                str(row[f'xml{i}_'])
                for i in range(1, 7)
                if pd.notna(row[f'xml{i}_']) and str(row[f'xml{i}_']) != ''
            ]),
            axis=1
        )

        # 2. 解密密钥
        chunk_df['secret_key'] = chunk_df['key_'].apply(lambda x: decrypt_with_rsa(private_key, x))

        # 3. 解密各个字段
        chunk_df['combined'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['combined'], row['secret_key']),axis=1)
        chunk_df['clickitem_'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['clickitem_'], row['secret_key']),axis=1)
        chunk_df['nativeinfo_'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['nativeinfo_'], row['secret_key']),axis=1)
        chunk_df['nickname_'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['nickname_'], row['secret_key']),axis=1)
        chunk_df['signature_'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['signature_'], row['secret_key']),axis=1)
        chunk_df['categories_'] = chunk_df.apply(lambda row: decode_with_pycryptodome(row['categories_'], row['secret_key']),axis=1)

        # 4. 清理中间列
        cols_to_drop = [f'xml{i}_' for i in range(1, 7)]
        chunk_df = chunk_df.drop(columns=[col for col in cols_to_drop if col in chunk_df.columns])

        return chunk_df.to_dict('records')

    except Exception as e:
        print(f"处理数据块时出错: {e}")
        return []

def process_dataframe_multiprocess(df: pd.DataFrame) -> pd.DataFrame:
    """使用多进程处理DataFrame"""
    print(f"开始多进程处理数据，数据量: {len(df)} 行")
    print(f"使用 {CPU_COUNT} 个进程，每个批次 {CHUNK_SIZE} 行")

    # 将DataFrame分割成多个块
    chunks = []
    for i in range(0, len(df), CHUNK_SIZE):
        chunk = df.iloc[i:i+CHUNK_SIZE].to_dict('records')
        chunks.append(chunk)

    print(f"数据分割成 {len(chunks)} 个块")

    # 使用多进程处理
    processed_chunks = []
    with ProcessPoolExecutor(max_workers=CPU_COUNT, mp_context=mp.get_context('spawn')) as executor:
        # 提交所有任务
        future_to_chunk = {
            executor.submit(process_chunk_worker, chunk, credential_file_path): i
            for i, chunk in enumerate(chunks)
        }

        # 收集结果
        for future in as_completed(future_to_chunk):
            chunk_idx = future_to_chunk[future]
            try:
                result = future.result()
                processed_chunks.append(result)
                OssAttrInc(339497, 20, 1)
                print(f"完成处理块 {chunk_idx + 1}/{len(chunks)}")
            except Exception as e:
                print(f"处理块 {chunk_idx} 时出错: {e}")
                processed_chunks.append([])

    # 合并结果
    all_records = []
    for chunk_records in processed_chunks:
        all_records.extend(chunk_records)

    result_df = pd.DataFrame(all_records)
    print(f"多进程处理完成，结果数据量: {len(result_df)} 行")

    return result_df

def process_dataframe(df: pd.DataFrame) -> pd.DataFrame:
    """原始的单进程处理函数（保留作为备选）"""
    # 1. 拼接所有列（处理可能的空值）
    df['combined'] = df.apply(
        lambda row: ''.join([
            str(row[f'xml{i}_'])
            for i in range(1, 7)
            if pd.notna(row[f'xml{i}_']) and str(row[f'xml{i}_']) != ''
        ]),
        axis=1
    )

    df['secret_key'] = df['key_'].apply(lambda x: decrypt_with_rsa(private_key, x))
    # 2. 逆向操作
    df['combined'] = df.apply(lambda row: decode_with_pycryptodome(row['combined'], row['secret_key']),axis=1)
    df['clickitem_'] = df.apply(lambda row: decode_with_pycryptodome(row['clickitem_'], row['secret_key']),axis=1)
    df['nativeinfo_'] = df.apply(lambda row: decode_with_pycryptodome(row['nativeinfo_'], row['secret_key']),axis=1)
    df['nickname_'] = df.apply(lambda row: decode_with_pycryptodome(row['nickname_'], row['secret_key']),axis=1)
    df['signature_'] = df.apply(lambda row: decode_with_pycryptodome(row['signature_'], row['secret_key']),axis=1)
    df['categories_'] = df.apply(lambda row: decode_with_pycryptodome(row['categories_'], row['secret_key']),axis=1)

    # 3. 清理中间列（可选）
    cols_to_drop = [f'xml{i}_' for i in range(1, 7)]
    df = df.drop(columns=[col for col in cols_to_drop if col in df.columns])
    return df

def js_decode_worker(chunk_data, js_file_path):
    """多进程JavaScript解码工作函数"""
    try:
        # 在子进程中初始化JavaScript环境
        ctx = MiniRacer()
        with open(js_file_path, "r", encoding='utf-8') as file:
            js_code = file.read()
        ctx.eval(js_code)

        def decode_func(x):
            try:
                return ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", x) if pd.notna(x) else None
            except Exception as e:
                print(f"JS解码错误: {e}")
                OssAttrInc(535937,16,1)
                return None

        # 处理数据块
        results = []
        for record in chunk_data:
            decoded_value = decode_func(record.get('combined'))
            record['decoded'] = decoded_value
            results.append(record)

        return results

    except Exception as e:
        print(f"JS解码工作进程出错: {e}")
        OssAttrInc(535937,17,1)
        return chunk_data  # 返回原始数据

def decode_csv_with_js_multiprocess(df):
    """使用多进程进行JavaScript解码"""
    print(f"开始多进程JS解码，数据量: {len(df)} 行")

    # 将DataFrame分割成多个块
    chunks = []
    for i in range(0, len(df), CHUNK_SIZE):
        chunk = df.iloc[i:i+CHUNK_SIZE].to_dict('records')
        chunks.append(chunk)

    print(f"JS解码数据分割成 {len(chunks)} 个块")

    # 使用多进程处理
    js_file_path = "./js/index_xml_0527.global.js"
    processed_chunks = []

    with ProcessPoolExecutor(max_workers=min(CPU_COUNT, 32), mp_context=mp.get_context('spawn')) as executor:  # JS解码限制进程数
        # 提交所有任务
        future_to_chunk = {
            executor.submit(js_decode_worker, chunk, js_file_path): i
            for i, chunk in enumerate(chunks)
        }

        # 收集结果
        for future in as_completed(future_to_chunk):
            chunk_idx = future_to_chunk[future]
            try:
                result = future.result()
                processed_chunks.append(result)
                print(f"完成JS解码块 {chunk_idx + 1}/{len(chunks)}")
            except Exception as e:
                print(f"JS解码块 {chunk_idx} 时出错: {e}")
                processed_chunks.append([])

    # 合并结果
    all_records = []
    for chunk_records in processed_chunks:
        all_records.extend(chunk_records)

    result_df = pd.DataFrame(all_records)
    print(f"多进程JS解码完成，结果数据量: {len(result_df)} 行")

    return result_df

def decode_csv_with_js(df):
    """原始的单进程JavaScript解码函数（保留作为备选）"""
    # 初始化 JavaScript 环境（只初始化一次）
    ctx = MiniRacer()
    js_code = ""
    # 读取 JavaScript 代码
    with open("./js/index_xml_0527.global.js", "r", encoding='utf-8') as file:
        js_code = file.read()
    ctx.eval(js_code)
    # 使用 apply 应用 JavaScript 函数
    def decode_func(x):
        try:
            return ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", x) if pd.notna(x) else None
        except Exception as e:
            print(f"Error occurred for value: {x}")  # 打印报错的值
            print(e)
            return None

    df['decoded'] = df['combined'].apply(decode_func)
    return df


def new_process_dataframe(df):
    df[['decoded', 'clickitem_']] = df.apply(process_row, axis=1)
    return df

def process_row(row):
    native_info = row['nativeinfo_']
    decoded = row['decoded']
    if native_info and len(row.get('clickitem_', None)) == 0:
        # native_info is sth like '<native><tab-bar id=xxx>...</tab-bar></native>'
        new_html = f"""
        <page>
            <iframe>
                {decoded}
            </iframe>
            {native_info}
        </page>
        """
        return pd.Series([new_html, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])
    return pd.Series([decoded, row.get('clickitem_', None)], index=['decoded', 'clickitem_'])


def get_pickled_data(output_file_name: str, start_time: str, end_time: str, session_cgi_label: str = "cgi7", use_multiprocess: bool = True):
    # 打印当前时间
    init_time = datetime.now()
    # 无效跳转场景
    invalid_scenes = ['1011', '1012']
    invalid_scenes_str = ', '.join([f"'{scene}'" for scene in invalid_scenes])
    print(f"当前时间：{init_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"初始内存使用: {get_memory_usage():.2f} MB")
    print(f"使用多进程模式: {use_multiprocess}")

    # 根据会话标签决定哈希分区数
    hash_partition_num = 1
    if session_cgi_label.lower() == "cgi9":
        hash_partition_num = 2
    elif session_cgi_label.lower() == "cgi3_6_1":
        hash_partition_num = 2
    elif session_cgi_label.lower() == "cgi3_6_2":
        hash_partition_num = 5

    # 动态生成 SQL 查询
    sql_base = f"""
    WITH session_prefix_stats AS (
        SELECT
        splitByString('#', sessionid_)[1] AS session_prefix
        FROM dw_luban.log_34771 t
        WHERE hour_ >= toDateTime('{start_time}')
        AND hour_ < toDateTime('{end_time}')
        AND sessionid_ LIKE '%#{session_cgi_label.upper()}%'
        AND scene_ not in ({invalid_scenes_str})
        GROUP BY session_prefix
        HAVING COUNT(*) <= 20
    ), sparse_appids AS (
        SELECT appid_
        FROM dw_luban.log_34771
        WHERE hour_ >= toDateTime('{start_time}')
            AND hour_ < toDateTime('{end_time}')
            AND sessionid_ LIKE '%#{session_cgi_label.upper()}%'
            AND scene_ not in ({invalid_scenes_str})
        GROUP BY appid_
        HAVING COUNT(DISTINCT splitByString('#', sessionid_)[1]) < 100
    )
    SELECT *
    FROM dw_luban.log_34771 t
    WHERE hour_ >= toDateTime('{start_time}')
        AND hour_ < toDateTime('{end_time}')
        AND sessionid_ LIKE '%#{session_cgi_label.upper()}%'
        AND splitByString('#', sessionid_)[1] IN (SELECT session_prefix FROM session_prefix_stats)
        AND (
        t.appid_ IN (SELECT appid_ FROM sparse_appids) OR
        MOD(ABS(cityHash64(concat(t.appid_, splitByString('#', t.sessionid_)[1]))), {hash_partition_num}) = 0
        )
    AND scene_ not in ({invalid_scenes_str})
    """
    '''
    查询过去某一天某个cgi版本的session_prefix_stats会话，并进行抽样
    1、session_prefix_stats：一次交互次数少于20次的会话，过滤掉太长的session
    2、sparse_appids：被使用次数少于100的appid直接拿进来
    3、cgi9哈希抽样一半，cgi3_6_1抽样20%，粒度可以控制
    '''

    print("SQL查询:")
    print(sql_base)

    # 获取数据
    for i in range(5):
        try:
            print(f"尝试第{i+1}次获取数据...")
            df = rawins.query_dataframe(sql_base, pretty=False, use_alias=False)
            OssAttrInc(535937,i,1)
            break
        except Exception as e:
            print("失败等待5s")
            time.sleep(5)
    else:
        print("获取数据失败！")
        raise

    # 打印获取到数据的时间
    got_data_time = datetime.now()
    print(f"获取到数据的时间：{got_data_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"获取数据耗时：{got_data_time - init_time}")
    print(f"获取数据后内存使用: {get_memory_usage():.2f} MB")

    print(f"原始数据形状: {df.shape}")
    OssAttrInc(535937,5,df.shape[0])
    print(f"数据列: {df.columns.tolist()}")

    # 选择处理方式
    if use_multiprocess and len(df) > 5000:  # 数据量大时使用多进程
        df = process_dataframe_multiprocess(df)
    else:
        df = process_dataframe(df)

    # 处理df的耗时
    processed_time = datetime.now()
    print(f"处理数据的时间：{processed_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"处理数据耗时：{processed_time - got_data_time}")
    print(f"处理数据后内存使用: {get_memory_usage():.2f} MB")
    print(f"处理后数据形状: {df.shape}")
    OssAttrInc(535937,6,df.shape[0])
    OssAttrInc(535937, 7, int((processed_time - got_data_time).total_seconds())) 
    # 过滤空值
    df = df[df[['combined', 'clickitem_', 'nativeinfo_']].notna().all(axis=1)]
    print('过滤空值后: ', df.shape)
    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'combined', 'nickname_', 'signature_', 'categories_', 'path_', 'scene_']]

    # JavaScript解码
    if use_multiprocess and len(df) > 5000:  # 数据量大时使用多进程
        df = decode_csv_with_js_multiprocess(df)
    else:
        df = decode_csv_with_js(df)

    # 解码耗时
    decoded_time = datetime.now()
    print(f"解码数据的时间：{decoded_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"解码数据耗时：{decoded_time - processed_time}")
    print(f"解码数据后内存使用: {get_memory_usage():.2f} MB")
    OssAttrInc(535937, 8, df.shape[0])
    OssAttrInc(535937, 9, int((decoded_time - processed_time).total_seconds()))
    
    df = df[df[['decoded']].notna().all(axis=1)]
    print('解码后过滤:', df.shape)
    print('====='*5)

    df = df[['appid_', 'sessionid_', 'clickitem_', 'length_', 'nativeinfo_', 'decoded',  'nickname_', 'signature_', 'categories_', 'path_', 'scene_']]
    print(f"选择列后数据形状: {df.shape}")
    df = df[df.length_ < 1024*6]
    print('过滤长度后:', df.shape)

    df = new_process_dataframe(df)
    # new process 耗时
    new_process_time = datetime.now()
    print(f"新处理数据的时间：{new_process_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"新处理数据耗时：{new_process_time - decoded_time}")
    print(f"最终数据形状: {df.shape}")
    print(f"最终内存使用: {get_memory_usage():.2f} MB")
    OssAttrInc(535937, 10, int((new_process_time - decoded_time).total_seconds()))
    # 保存 DataFrame 到动态生成的文件名
    df.to_pickle(output_file_name)
    pickle_size = os.path.getsize(output_file_name) / (1024**3)
    total_time = datetime.now() - init_time
    print(f"DataFrame 已保存到文件: {output_file_name}")
    print(f"总耗时: {total_time}")
    print(f"平均处理速度: {len(df) / total_time.total_seconds():.2f} 行/秒")
    OssAttrInc(535937, 11, int(total_time.total_seconds())) 
    OssAttrInc(535937, 18, int(pickle_size)) 
    OssAttrInc(535937, 20, int(len(df)))

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Get Pickled Data with Multiprocessing Optimization")
    parser.add_argument("--output_file_name", type=str, help="output_file_name")
    parser.add_argument("--start_time", type=str, help="start_time")
    parser.add_argument("--end_time", type=str, help="end_time")
    parser.add_argument("--session_cgi_label", type=str, help="session_cgi_label", default="cgi7")
    parser.add_argument("--use_multiprocess", type=bool, help="是否使用多进程加速", default=True)
    parser.add_argument("--chunk_size", type=int, help="每个批次处理的行数", default=1000)
    parser.add_argument("--max_workers", type=int, help="最大进程数", default=None)

    print("开始处理....")
    args = parser.parse_args()
    print("参数配置:")
    print(f"  输出文件: {args.output_file_name}")
    print(f"  会话标签: {args.session_cgi_label}")
    print(f"  多进程模式: {args.use_multiprocess}")
    print(f"  批次大小: {args.chunk_size}")
    print(f"  最大进程数: {args.max_workers}")

    # 更新配置
    chunk_size = args.chunk_size if args.chunk_size else CHUNK_SIZE
    max_workers = min(args.max_workers, mp.cpu_count()) if args.max_workers else CPU_COUNT

    file_name = args.output_file_name
    if not file_name.endswith(".pickle"):
        file_name += ".pickle"

    print(f"系统配置: CPU核心数={mp.cpu_count()}, 使用进程数={max_workers}, 批次大小={chunk_size}")

    get_pickled_data(file_name, args.start_time, args.end_time, args.session_cgi_label, args.use_multiprocess)
